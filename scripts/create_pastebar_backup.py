#!/usr/bin/env python3
"""
创建 PasteBar 兼容的备份文件

将 EcoPaste 和 Ditto 数据库转换为 PasteBar 可以导入的备份格式

使用方法:
python3 create_pastebar_backup.py --ecopaste /path/to/EcoPaste.db --ditto /path/to/Ditto.db --output /path/to/output.zip
"""

import sqlite3
import sys
import os
import hashlib
import time
import argparse
import zipfile
import tempfile
import shutil
from datetime import datetime
import uuid

def generate_uuid():
    """生成唯一ID"""
    return str(uuid.uuid4())

def calculate_hash(content):
    """计算内容哈希"""
    if not content:
        return None
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def get_preview_text(text, max_length=150):
    """生成预览文本"""
    if not text:
        return None
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length] + "..."

def parse_ecopaste_time(time_str):
    """解析 EcoPaste 时间格式"""
    try:
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        return int(dt.timestamp() * 1000)
    except:
        return int(time.time() * 1000)

def parse_ditto_time(timestamp):
    """解析 Ditto 时间戳"""
    try:
        return int(timestamp * 1000)
    except:
        return int(time.time() * 1000)

def create_pastebar_database(temp_db_path):
    """创建 PasteBar 数据库结构"""
    conn = sqlite3.connect(temp_db_path)
    cursor = conn.cursor()
    
    # 创建 clipboard_history 表
    cursor.execute("""
        CREATE TABLE clipboard_history (
            history_id VARCHAR(50) PRIMARY KEY NOT NULL,
            title VARCHAR(255),
            value VARCHAR(255),
            value_preview VARCHAR(150),
            value_more_preview_lines INT DEFAULT 0,
            value_more_preview_chars INT DEFAULT 0,
            value_hash VARCHAR(255),
            
            is_image BOOLEAN DEFAULT FALSE,
            image_path_full_res VARCHAR(255),
            image_data_low_res BLOB,
            image_preview_height INT DEFAULT 0,
            image_height INT DEFAULT 0,
            image_width INT DEFAULT 0,
            image_data_url VARCHAR(255),
            image_hash VARCHAR(255),

            is_image_data BOOLEAN DEFAULT FALSE,
            is_masked BOOLEAN DEFAULT FALSE,
            is_text BOOLEAN DEFAULT FALSE,
            is_code BOOLEAN DEFAULT FALSE,
            is_link BOOLEAN DEFAULT FALSE,
            is_video BOOLEAN DEFAULT FALSE,
            has_emoji BOOLEAN DEFAULT FALSE,
            has_masked_words BOOLEAN DEFAULT FALSE,
            is_pinned BOOLEAN DEFAULT FALSE,
            is_favorite BOOLEAN DEFAULT FALSE,

            links TEXT,
            
            detected_language VARCHAR(20),
            pinned_order_number INT DEFAULT 0,
                
            created_at BIGINT NOT NULL,
            updated_at BIGINT NOT NULL,
            created_date TIMESTAMP NOT NULL,
            updated_date TIMESTAMP NOT NULL,
            
            history_options TEXT,
            copied_from_app VARCHAR(255)
        )
    """)
    
    # 创建其他必要的表
    cursor.execute("""
        CREATE TABLE settings (
            name TEXT PRIMARY KEY NOT NULL UNIQUE,
            value_text TEXT,
            value_bool BOOLEAN,
            value_int INTEGER
        )
    """)
    
    conn.commit()
    return conn

def import_ecopaste_to_temp_db(ecopaste_db_path, temp_cursor):
    """将 EcoPaste 数据导入临时数据库"""
    print("处理 EcoPaste 数据...")
    
    eco_conn = sqlite3.connect(ecopaste_db_path)
    eco_cursor = eco_conn.cursor()
    
    try:
        eco_cursor.execute("""
            SELECT id, type, value, search, count, width, height, 
                   favorite, createTime, note, subtype
            FROM history 
            ORDER BY createTime DESC
        """)
        
        eco_records = eco_cursor.fetchall()
        print(f"找到 {len(eco_records)} 条 EcoPaste 记录")
        
        imported_count = 0
        skipped_count = 0
        
        for record in eco_records:
            eco_id, content_type, value, search, count, width, height, favorite, create_time, note, subtype = record
            
            if not value or value.strip() == "":
                skipped_count += 1
                continue
            
            # 检测内容类型
            is_text = content_type in ['text', 'html', 'rtf']
            is_image = content_type == 'image'
            is_link = 'http' in str(value).lower() if value else False
            
            # 生成数据
            history_id = generate_uuid()
            created_timestamp = parse_ecopaste_time(create_time)
            value_hash = calculate_hash(value)
            value_preview = get_preview_text(value)
            title = search if search else value_preview
            
            insert_data = {
                'history_id': history_id,
                'title': title,
                'value': value,
                'value_preview': value_preview,
                'value_hash': value_hash,
                'is_image': is_image,
                'image_width': width,
                'image_height': height,
                'is_text': is_text,
                'is_link': is_link,
                'is_favorite': bool(favorite),
                'created_at': created_timestamp,
                'updated_at': created_timestamp,
                'created_date': datetime.fromtimestamp(created_timestamp / 1000),
                'updated_date': datetime.fromtimestamp(created_timestamp / 1000),
                'copied_from_app': 'EcoPaste'
            }
            
            try:
                temp_cursor.execute("""
                    INSERT INTO clipboard_history (
                        history_id, title, value, value_preview, value_hash,
                        is_image, image_width, image_height, is_text, is_link,
                        is_favorite, created_at, updated_at, created_date, updated_date,
                        copied_from_app
                    ) VALUES (
                        :history_id, :title, :value, :value_preview, :value_hash,
                        :is_image, :image_width, :image_height, :is_text, :is_link,
                        :is_favorite, :created_at, :updated_at, :created_date, :updated_date,
                        :copied_from_app
                    )
                """, insert_data)
                
                imported_count += 1
                
                if imported_count % 1000 == 0:
                    print(f"EcoPaste: 已处理 {imported_count} 条记录...")
                    
            except sqlite3.IntegrityError:
                skipped_count += 1
            except Exception as e:
                print(f"EcoPaste 处理记录失败 {eco_id}: {e}")
                skipped_count += 1
        
        print(f"EcoPaste 处理完成: 成功 {imported_count} 条, 跳过 {skipped_count} 条")
        return imported_count, skipped_count
        
    finally:
        eco_conn.close()

def import_ditto_to_temp_db(ditto_db_path, temp_cursor):
    """将 Ditto 数据导入临时数据库"""
    print("处理 Ditto 数据...")
    
    ditto_conn = sqlite3.connect(ditto_db_path)
    ditto_cursor = ditto_conn.cursor()
    
    try:
        ditto_cursor.execute("""
            SELECT lID, lDate, mText, lDontAutoDelete, bIsGroup, lParentID, 
                   QuickPasteText, lastPasteDate
            FROM Main 
            WHERE bIsGroup = 0 AND lParentID = -1
            ORDER BY lDate DESC
        """)
        
        ditto_records = ditto_cursor.fetchall()
        print(f"找到 {len(ditto_records)} 条 Ditto 记录")
        
        imported_count = 0
        skipped_count = 0
        
        for record in ditto_records:
            ditto_id, date_timestamp, text, dont_auto_delete, is_group, parent_id, quick_paste_text, last_paste_date = record
            
            if not text or text.strip() == "":
                skipped_count += 1
                continue
            
            # 获取数据格式
            ditto_cursor.execute("""
                SELECT strClipBoardFormat FROM Data WHERE lParentID = ?
            """, (ditto_id,))
            
            data_formats = [row[0] for row in ditto_cursor.fetchall()]
            
            # 检测内容类型
            is_image = any(fmt in ['CF_DIB', 'CF_BITMAP', 'PNG', 'JPEG'] for fmt in data_formats)
            is_text = not is_image
            is_link = text and (text.startswith('http://') or text.startswith('https://'))
            
            # 生成数据
            history_id = generate_uuid()
            created_timestamp = parse_ditto_time(date_timestamp)
            value_hash = calculate_hash(text)
            value_preview = get_preview_text(text)
            title = quick_paste_text if quick_paste_text else value_preview
            
            insert_data = {
                'history_id': history_id,
                'title': title,
                'value': text,
                'value_preview': value_preview,
                'value_hash': value_hash,
                'is_image': is_image,
                'image_width': None,
                'image_height': None,
                'is_text': is_text,
                'is_link': is_link,
                'is_favorite': bool(dont_auto_delete),
                'created_at': created_timestamp,
                'updated_at': created_timestamp,
                'created_date': datetime.fromtimestamp(created_timestamp / 1000),
                'updated_date': datetime.fromtimestamp(created_timestamp / 1000),
                'copied_from_app': 'Ditto'
            }
            
            try:
                temp_cursor.execute("""
                    INSERT INTO clipboard_history (
                        history_id, title, value, value_preview, value_hash,
                        is_image, image_width, image_height, is_text, is_link,
                        is_favorite, created_at, updated_at, created_date, updated_date,
                        copied_from_app
                    ) VALUES (
                        :history_id, :title, :value, :value_preview, :value_hash,
                        :is_image, :image_width, :image_height, :is_text, :is_link,
                        :is_favorite, :created_at, :updated_at, :created_date, :updated_date,
                        :copied_from_app
                    )
                """, insert_data)
                
                imported_count += 1
                
                if imported_count % 1000 == 0:
                    print(f"Ditto: 已处理 {imported_count} 条记录...")
                    
            except sqlite3.IntegrityError:
                skipped_count += 1
            except Exception as e:
                print(f"Ditto 处理记录失败 {ditto_id}: {e}")
                skipped_count += 1
        
        print(f"Ditto 处理完成: 成功 {imported_count} 条, 跳过 {skipped_count} 条")
        return imported_count, skipped_count
        
    finally:
        ditto_conn.close()

def main():
    parser = argparse.ArgumentParser(description='创建 PasteBar 兼容的备份文件')
    parser.add_argument('--ecopaste', help='EcoPaste 数据库路径')
    parser.add_argument('--ditto', help='Ditto 数据库路径')
    parser.add_argument('--output', required=True, help='输出备份文件路径 (.zip)')
    
    args = parser.parse_args()
    
    if not args.ecopaste and not args.ditto:
        print("错误: 必须指定至少一个源数据库 (--ecopaste 或 --ditto)")
        sys.exit(1)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_db_path = os.path.join(temp_dir, "pastebar-db.data")
        
        # 创建 PasteBar 数据库
        temp_conn = create_pastebar_database(temp_db_path)
        temp_cursor = temp_conn.cursor()
        
        total_imported = 0
        total_skipped = 0
        
        try:
            # 处理 EcoPaste 数据
            if args.ecopaste and os.path.exists(args.ecopaste):
                imported, skipped = import_ecopaste_to_temp_db(args.ecopaste, temp_cursor)
                total_imported += imported
                total_skipped += skipped
            elif args.ecopaste:
                print(f"警告: EcoPaste 数据库文件不存在: {args.ecopaste}")
            
            # 处理 Ditto 数据
            if args.ditto and os.path.exists(args.ditto):
                imported, skipped = import_ditto_to_temp_db(args.ditto, temp_cursor)
                total_imported += imported
                total_skipped += skipped
            elif args.ditto:
                print(f"警告: Ditto 数据库文件不存在: {args.ditto}")
            
            # 提交数据库更改
            temp_conn.commit()
            temp_conn.close()
            
            # 创建 ZIP 备份文件
            print(f"创建备份文件: {args.output}")
            with zipfile.ZipFile(args.output, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(temp_db_path, "pastebar-db.data")
            
            print(f"\n🎉 备份文件创建完成!")
            print(f"文件位置: {args.output}")
            print(f"总计处理: {total_imported} 条记录")
            print(f"跳过记录: {total_skipped} 条记录")
            print("\n📋 使用方法:")
            print("1. 打开 PasteBar 应用")
            print("2. 进入 设置 > 备份和恢复")
            print("3. 点击 '从文件恢复...'")
            print(f"4. 选择文件: {args.output}")
            print("5. 确认恢复操作")
            
        except Exception as e:
            print(f"创建备份文件时发生错误: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
最简单的方法：直接在成功的数据库中插入Ditto数据
"""

import sqlite3
import hashlib
import random
import string
from datetime import datetime

def generate_uuid():
    """生成与成功案例相同格式的UUID"""
    chars = string.ascii_letters + string.digits
    return ''.join(random.choices(chars, k=21))

def main():
    # 连接数据库
    pastebar_conn = sqlite3.connect('/Users/<USER>/Downloads/pastebar-db.data')
    ditto_conn = sqlite3.connect('/Users/<USER>/Downloads/Ditto.db')
    
    pastebar_cursor = pastebar_conn.cursor()
    ditto_cursor = ditto_conn.cursor()
    
    # 查询Ditto数据
    ditto_cursor.execute("""
        SELECT lID, lDate, mText, lDontAutoDelete, QuickPasteText
        FROM Main 
        WHERE bIsGroup = 0 AND lParentID = -1 AND mText IS NOT NULL AND mText != ''
        ORDER BY lDate DESC
        LIMIT 1000
    """)
    
    records = ditto_cursor.fetchall()
    print(f"找到 {len(records)} 条记录")
    
    count = 0
    for record in records:
        ditto_id, date_timestamp, text, dont_auto_delete, quick_paste_text = record
        
        if not text or text.strip() == "":
            continue
            
        # 生成数据
        history_id = generate_uuid()
        
        # 时间戳转换
        if date_timestamp < 10000000000:
            created_at = date_timestamp * 1000
        else:
            created_at = date_timestamp
            
        dt = datetime.fromtimestamp(created_at / 1000)
        created_date = dt.strftime('%Y-%m-%d %H:%M:%S.%f')
        
        # 处理文本
        value = text
        value_preview = text[:150] if text else ""
        value_hash = hashlib.sha256(text.encode('utf-8')).hexdigest()
        
        # 标题
        title = quick_paste_text if quick_paste_text else ""
        
        # 类型检测
        is_text = 1
        is_link = 1 if 'http' in text else 0
        is_favorite = 1 if dont_auto_delete else 0
        
        # 插入数据
        try:
            pastebar_cursor.execute("""
                INSERT INTO clipboard_history (
                    history_id, title, value, value_preview, value_hash,
                    is_text, is_link, is_favorite,
                    created_at, updated_at, created_date, updated_date,
                    copied_from_app
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                history_id, title, value, value_preview, value_hash,
                is_text, is_link, is_favorite,
                created_at, created_at, created_date, created_date,
                "Ditto"
            ))
            count += 1
            if count % 100 == 0:
                print(f"已插入 {count} 条记录")
        except Exception as e:
            print(f"插入失败: {e}")
    
    pastebar_conn.commit()
    pastebar_conn.close()
    ditto_conn.close()
    
    print(f"完成！共插入 {count} 条记录")

if __name__ == "__main__":
    main()
